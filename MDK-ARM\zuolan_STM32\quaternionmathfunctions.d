.\zuolan_stm32\quaternionmathfunctions.o: D:/123456/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/QuaternionMathFunctions.c
.\zuolan_stm32\quaternionmathfunctions.o: D:/123456/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/arm_quaternion_norm_f32.c
.\zuolan_stm32\quaternionmathfunctions.o: ..\Drivers\CMSIS\DSP\Include\dsp/quaternion_math_functions.h
.\zuolan_stm32\quaternionmathfunctions.o: ..\Drivers\CMSIS\DSP\Include\arm_math_types.h
.\zuolan_stm32\quaternionmathfunctions.o: ../Drivers/CMSIS/Include/cmsis_compiler.h
.\zuolan_stm32\quaternionmathfunctions.o: D:\123456\ARM\ARM5\Bin\..\include\stdint.h
.\zuolan_stm32\quaternionmathfunctions.o: ../Drivers/CMSIS/Include/cmsis_armcc.h
.\zuolan_stm32\quaternionmathfunctions.o: D:\123456\ARM\ARM5\Bin\..\include\string.h
.\zuolan_stm32\quaternionmathfunctions.o: D:\123456\ARM\ARM5\Bin\..\include\math.h
.\zuolan_stm32\quaternionmathfunctions.o: D:\123456\ARM\ARM5\Bin\..\include\float.h
.\zuolan_stm32\quaternionmathfunctions.o: D:\123456\ARM\ARM5\Bin\..\include\limits.h
.\zuolan_stm32\quaternionmathfunctions.o: ..\Drivers\CMSIS\DSP\Include\arm_math_memory.h
.\zuolan_stm32\quaternionmathfunctions.o: ..\Drivers\CMSIS\DSP\Include\dsp/none.h
.\zuolan_stm32\quaternionmathfunctions.o: ..\Drivers\CMSIS\DSP\Include\dsp/utils.h
.\zuolan_stm32\quaternionmathfunctions.o: D:/123456/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/arm_quaternion_inverse_f32.c
.\zuolan_stm32\quaternionmathfunctions.o: D:/123456/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/arm_quaternion_conjugate_f32.c
.\zuolan_stm32\quaternionmathfunctions.o: D:/123456/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/arm_quaternion_normalize_f32.c
.\zuolan_stm32\quaternionmathfunctions.o: D:/123456/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/arm_quaternion_product_single_f32.c
.\zuolan_stm32\quaternionmathfunctions.o: D:/123456/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/arm_quaternion_product_f32.c
.\zuolan_stm32\quaternionmathfunctions.o: D:/123456/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/arm_quaternion2rotation_f32.c
