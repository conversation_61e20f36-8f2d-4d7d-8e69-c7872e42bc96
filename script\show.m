% 打开文件
fileID = fopen('data3.log', 'r');

% 读取文件内容（假设每行一个数值）
data = textscan(fileID, '%f');

% 关闭文件
fclose(fileID);

% 将数据转换为向量
data_vector = data{1};

% 计算平均值
data_mean = mean(data_vector);
fprintf('数据平均值: %.4f\n', data_mean);  % 控制台输出平均值

% 创建图形界面
figure;
plot(data_vector, 'b-', 'LineWidth', 1.5);  % 蓝色实线绘制数据
hold on;
yline(data_mean, 'r--', 'LineWidth', 1.5, ...  % 红色虚线标注平均值
    'Label', sprintf('平均值 = %.4f', data_mean));  % 在线上显示数值

% 图形美化
title('数据可视化 (含平均值)');
xlabel('数据索引');
ylabel('数值');
grid on;
legend('原始数据', 'Location', 'best');  % 添加图例

% 可选：突出显示超出平均值的点
above_mean = data_vector > data_mean;
plot(find(above_mean), data_vector(above_mean), 'ro', ...
    'MarkerSize', 4, 'MarkerFaceColor', 'r');
