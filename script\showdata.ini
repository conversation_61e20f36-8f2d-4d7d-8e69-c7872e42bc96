FUNC void showdata(void){
	int idx;
	exec("log > ../script/data1.log");
	for(idx=0;idx<500;idx++){
		printf("%f\n",data_freq[idx]);
	}
	exec("log off");
}
FUNC void showdata1(void){
	int idx;
	exec("log > ../script/data2.log");
	for(idx=0;idx<500;idx++){
		printf("%f\n",data_vpp[idx]);
	}
	exec("log off");
}
FUNC void showdata2(void){
	int idx;
	exec("log > ../script/data3.log");
	for(idx=0;idx<1023;idx++){
		printf("%f\n",(float)fifo_data1[idx]);
	}
	exec("log off");
}
//showdata();
//showdata1();
showdata2();