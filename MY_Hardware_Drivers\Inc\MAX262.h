//-----------------------------------------------------------------
// MAX262程序头文件
// 头文件名: MAX262.h
// 作    者: 凌智电子
// 编写时间: 2014-01-28
// 修改日期:
//-----------------------------------------------------------------
//    MAX262模块     STM32核心板
//      GND   <-->   GND
//      LE    <--    PF6
//      A3    <--    PF8
//      A2    <--    PB15
//      A1    <--    PG12
//      A0    <--    PD4
//      D1    <--    PH8
//      D0    <--    PD14
//      WR    <--    PD0
//

#ifndef _MAX262_H
#define _MAX262_H

#include "commond_init.h"
#include "cmd_to_fun.h"
#include "bsp_system.h"
#include "ad_measure.h"
//-----------------------------------------------------------------
// 数据位 D0, D1
#define D0_H   HAL_GPIO_WritePin(GPIOB, GPIO_PIN_14, GPIO_PIN_SET)
#define D0_L   HAL_GPIO_WritePin(GPIOB, GPIO_PIN_14, GPIO_PIN_RESET)

#define D1_H   HAL_GPIO_WritePin(GPIOB, GPIO_PIN_15, GPIO_PIN_SET)
#define D1_L   HAL_GPIO_WritePin(GPIOB, GPIO_PIN_15, GPIO_PIN_RESET)

// 地址位 A0 ~ A3
#define A0_H   HAL_GPIO_WritePin(GPIOI, GPIO_PIN_3, GPIO_PIN_SET)
#define A0_L   HAL_GPIO_WritePin(GPIOI, GPIO_PIN_3, GPIO_PIN_RESET)
#define A0_IS_L  (HAL_GPIO_ReadPin(GPIOI, GPIO_PIN_3) == GPIO_PIN_RESET)

#define A1_H   HAL_GPIO_WritePin(GPIOI, GPIO_PIN_5, GPIO_PIN_SET)
#define A1_L   HAL_GPIO_WritePin(GPIOI, GPIO_PIN_5, GPIO_PIN_RESET)
#define A1_IS_H  (HAL_GPIO_ReadPin(GPIOI, GPIO_PIN_5) == GPIO_PIN_SET)

#define A2_H   HAL_GPIO_WritePin(GPIOI, GPIO_PIN_6, GPIO_PIN_SET)
#define A2_L   HAL_GPIO_WritePin(GPIOI, GPIO_PIN_6, GPIO_PIN_RESET)

#define A3_H   HAL_GPIO_WritePin(GPIOI, GPIO_PIN_7, GPIO_PIN_SET)
#define A3_L   HAL_GPIO_WritePin(GPIOI, GPIO_PIN_7, GPIO_PIN_RESET)

// 控制引脚 LE 和 WR（GPIOE）
#define LE_H   HAL_GPIO_WritePin(GPIOI, GPIO_PIN_10, GPIO_PIN_SET)
#define LE_L   HAL_GPIO_WritePin(GPIOI, GPIO_PIN_10, GPIO_PIN_RESET)

#define WR_H   HAL_GPIO_WritePin(GPIOI, GPIO_PIN_9, GPIO_PIN_SET)
#define WR_L   HAL_GPIO_WritePin(GPIOI, GPIO_PIN_9, GPIO_PIN_RESET)

//-----------------------------------------------------------------
// 外部函数声明
//-----------------------------------------------------------------
void MAX262_Init(void);
extern void Filter1(u8 mode, float f, float q);
extern void Filter2(u8 mode, float f, float q);
extern u8 Fn1(float f);
extern u8 Fn2(float f);
extern u8 Qn(float q);
void MAX262_clock(float f0);
#endif

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
