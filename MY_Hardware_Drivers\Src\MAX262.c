//-----------------------------------------------------------------
// 程序描述:
//		MAX262驱动代码
// 作    者: 凌智电子
// 开始日期: 2018-05-24
// 完成日期: 2018-05-24
// 修改日期:
// 版    本: V1.0
//   - V1.0: 初步模板
// 调试工具: 凌智STM32+FPGA电子系统设计开发板、LZE_ST LINK2、MAX262模块
// 说    明:
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// 头文件包含
//-----------------------------------------------------------------
#include "MAX262.h"

#define fclk1 0.1f // 100k时钟频率,单位Hz
#define fclk2 0.1f // 100k时钟频率

//-----------------------------------------------------------------
// 功能程序区
//-----------------------------------------------------------------
// 初始化引脚
void MAX262_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
        //MAX262
  HAL_GPIO_WritePin(GPIOI, GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_3|GPIO_PIN_5|GPIO_PIN_6|GPIO_PIN_7, GPIO_PIN_RESET);
  
  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOB,GPIO_PIN_14|GPIO_PIN_15, GPIO_PIN_RESET);

  
  GPIO_InitStruct.Pin = GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_3|GPIO_PIN_5|GPIO_PIN_6|GPIO_PIN_7;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
  HAL_GPIO_Init(GPIOI, &GPIO_InitStruct);
  //max262
  GPIO_InitStruct.Pin = GPIO_PIN_14|GPIO_PIN_15;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct); 
}

//-----------------------------------------------------------------
// u8 Fn(float f)
//-----------------------------------------------------------------
// 函数功能: 计算f的频率控制字N
// 入口参数: 截止频率(中心频率) f
// 返 回 值: 控制字N
// 全局变量: 无
// 注意事项: 无
//-----------------------------------------------------------------
#define fclk1 0.1f // 100kHz
#define fclk2 0.1f // 100kHz
#define fN 63      // 频率控制字的最大值
// uint8_t Fn1(float f)  // f 单位为 kHz
// {
//     float N = (fclk1 / f) * 637.0f - 26.0f;
//     if (N < 0) N = 0;
//     if (N > 63) N = 63;
//     return (uint8_t)(N + 0.5f);
// }
// uint8_t Fn2(float f)  // f 单位为 kHz
// {
//     float N = (fclk2 / f) * 637.0f - 26.0f;
//     if (N < 0) N = 0;
//     if (N > 63) N = 63;
//     return (uint8_t)(N + 0.5f);
// }
//-----------------------------------------------------------------
// u8 Qn(float q)
//-----------------------------------------------------------------
// 函数功能: 计算Q的控制字N
// 入口参数: Q
// 返 回 值: 控制字N
// 全局变量: 无
// 注意事项: 无
//-----------------------------------------------------------------
uint8_t Qn(float q)
{
    float N = 128.0f - (64.0f / q);
    if (N < 0)
        N = 0;
    if (N > 127)
        N = 127;
    return (uint8_t)(N + 0.5f);
}
void Write_Address(uint8_t addr)
{
    (addr & 0x01) ? A0_H : A0_L;
    (addr & 0x02) ? A1_H : A1_L;
    (addr & 0x04) ? A2_H : A2_L;
    (addr & 0x08) ? A3_H : A3_L;
}
void Write_ControlBits(uint8_t ctrl)
{
    (ctrl & 0x01) ? D0_H : D0_L;
    (ctrl & 0x02) ? D1_H : D1_L;
}
void Clear_AddressAndData(void)
{
    A0_L;
    A1_L;
    A2_L;
    A3_L;
    D0_L;
    D1_L;
}

void MAX262_Write(uint8_t addr, uint8_t data2bit)
{
    Clear_AddressAndData();
    Write_Address(addr);
    HAL_Delay(1); // 可改为 Delay_us(1) 或 Delay_ns(200)
    WR_L;
    HAL_Delay(1);
    Write_ControlBits(data2bit & 0x03);
    HAL_Delay(1);
    WR_H;
}

//-----------------------------------------------------------------
// void Filter1(uint8_t mode, float q)
//-----------------------------------------------------------------
// 函数功能: 滤波器的模式、频率以及Q值的设置
// 入口参数: 模式 mode, 截止/中心频率 f, Q值 q
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 无
//-----------------------------------------------------------------
void Filter1(uint8_t mode, float f, float q)
{
    uint8_t sf = fN;    // 频率控制字（6位）
    uint8_t sq = Qn(q); // Q 控制字（7位）

    LE_H;
    HAL_Delay(1); // 锁存使能高电平
    WR_H;
    HAL_Delay(1); // 写使能高电平（准备）

    // 写 Mode（地址 0）
    MAX262_Write(0, mode & 0x03);

    // 写频率控制字 sf（地址 1~3，每次写 2bit）
    for (uint8_t i = 0; i < 3; i++)
        MAX262_Write(i + 1, (sf >> (2 * i)) & 0x03);

    // 写 Q 控制字 sq（地址 4~7，每次写 2bit）
    for (uint8_t i = 0; i < 4; i++)
        MAX262_Write(i + 4, (sq >> (2 * i)) & 0x03);

    LE_L;
    HAL_Delay(1); // 完成写入后锁存
}
void Filter2(uint8_t mode, float f, float q)
{
    uint8_t sf = fN;    // 频率控制字（6位）
    uint8_t sq = Qn(q); // Q控制字（7位）

    LE_H;
    HAL_Delay(1); // 锁存使能高电平
    WR_H;
    HAL_Delay(1); // 写使能准备高电平

    // 写模式控制字，地址 8
    MAX262_Write(8, mode & 0x03);

    // 写频率控制字（地址 9 ~ 11，每次写2位）
    for (uint8_t i = 0; i < 3; i++)
    {
        uint8_t data = (sf >> (2 * i)) & 0x03;
        MAX262_Write(9 + i, data);
    }

    // 写 Q 控制字（地址 12 ~ 15，每次写2位）
    for (uint8_t i = 0; i < 4; i++)
    {
        uint8_t data = (sq >> (2 * i)) & 0x03;
        MAX262_Write(12 + i, data);
    }

    LE_L;
    HAL_Delay(1); // 锁存完成
}

//void MAX262_clock(float f0)
//{
//    if (f0 == 0)
//        return;
//    float fclk = (fN + 26) * f0 * 3.1415926f / 2.0f;
//    unsigned int M = FREQ_CONSTANT * fclk / CLOCK_FREQ; // 频率控制字
//    FILTER_ENABLE();
//    HAL_Delay(1);
//    F_CLK_H = M >> 16;
//    HAL_Delay(1);
//    F_CLK_L = M & 0xFFFF;
//    HAL_Delay(1);
//}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
