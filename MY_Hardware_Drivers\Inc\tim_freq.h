//-----------------------------------------------------------------
// 程序描述:
// 		 独立键盘驱动程序头文件
// 作    者: 凌智电子
// 开始日期: 2018-08-04
// 完成日期: 2018-08-04
// 修改日期: 
// 当前版本: V2.0
// 历史版本:
//  - V1.0: (2018-08-04)定时器中断初始化，定时器中断服务函数
// 调试工具: 凌智STM32F429+Cyclone IV电子系统设计开发板、LZE_ST_LINK2
// 说    明: 
//    
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// 头文件包含
//-----------------------------------------------------------------
#ifndef __TIMER_H
#define __TIMER_H
#include "stm32f4xx_hal.h"
#include "commond_init.h"
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// 声明
//-----------------------------------------------------------------
extern TIM_HandleTypeDef TIM2_Handler;      //定时器2句柄 
extern TIM_HandleTypeDef TIM3_Handler;      //定时器3句柄 

#define MEASU_START     0xA5
#define MEASURING       0xAA
#define MEASU_END       0x55
   
#define TIM_CLK            ( 90000 )      // kHz
#define TUEN_TIME          ( 500   )      // ms
#define OVER_TIME          ( 2200  )      // ms
#define TUEN_TIME_CNT      (( TUEN_TIME * TIM_CLK ) >> 16 )
#define OVER_TIME_CNT      (( OVER_TIME * TIM_CLK ) >> 16 )

//-----------------------------------------------------------------
// 外部函数声明
//-----------------------------------------------------------------
void TIM3_Init(u16 arr,u16 psc);
void TIM2_Init(u16 arr,u16 psc);
void MY_TIM_init(void);
#endif
//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
