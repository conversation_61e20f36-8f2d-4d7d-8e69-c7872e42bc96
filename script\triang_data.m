% 定义参数
num_points = 1024;   % 一个周期的点数，可根据需求调整
bit_depth = 14;     % D/A 的位深度，常见如 8、10、12、14 等
min_value = 8192;   % 最小值对应数字值
max_value = 2^bit_depth - 1; % 计算对应位深度的最大值

% 生成一个周期的方波数据
% 前半段保持为最大值，后半段保持为min_value
x1 = ones(1, num_points/2) * max_value;
x2 = ones(1, num_points/2) * min_value;
data = [x1, x2];
data = round(data); % 转换为整数（因为要存入 MIF，一般是整数值）

% 将数据写入 MIF 文件
fileID = fopen('square_wave_1024_8192.mif', 'w'); % 打开文件，可修改文件名
fprintf(fileID, 'WIDTH = %d;\n', bit_depth); % 设置数据宽度
fprintf(fileID, 'DEPTH = %d;\n', num_points); % 设置数据深度
fprintf(fileID, 'ADDRESS_RADIX = UNS;\n');    % 地址基数为无符号数
fprintf(fileID, 'DATA_RADIX = UNS;\n');       % 数据基数为无符号数
fprintf(fileID, 'CONTENT BEGIN\n');

for i = 0:num_points-1
    fprintf(fileID, '%d : %d;\n', i, data(i+1)); % 写入每个数据点，注意 MATLAB 数组索引从 1 开始
end

fprintf(fileID, 'END;\n'); % 结束 MIF 文件内容
fclose(fileID); % 关闭文件

% 绘图查看生成的方波（可选，方便验证数据是否正确）
figure;
plot(data);
title('Generated Square Wave (Min Value = 8192)');
xlabel('Sample Index');
ylabel('Amplitude Value');
grid on;