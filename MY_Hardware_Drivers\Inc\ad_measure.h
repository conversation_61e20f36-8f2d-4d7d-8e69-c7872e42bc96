#ifndef __AD_H__
#define __AD_H__
#include "commond_init.h"
#include "cmd_to_fun.h"
#include "bsp_system.h"
extern float fifo_data1_f[FIFO_SIZE], fifo_data2_f[FIFO_SIZE]; // 采样结果转换为浮点数
extern float vol_amp1, vol_amp2;
extern u16 fifo_data1[FIFO_SIZE], fifo_data2[FIFO_SIZE]; 
extern float vol_amp1_duibi,vol_zl1;
void vpp_adc_parallel(float ad1_freq, float ad2_freq);
void ad_proc(void);
void setSamplingFrequency(float fre, int channel);
void readFIFOData(int channel, u16 *fifo_data, float *fifo_data_f);
#endif //__AD_H__
