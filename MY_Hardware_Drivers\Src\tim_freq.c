//-----------------------------------------------------------------
// 程序描述:
// 		 定时器中断驱动程序
// 作    者: 凌智电子
// 开始日期: 2018-08-04
// 完成日期: 2018-08-04
// 修改日期: 
// 当前版本: V1.0
// 历史版本:
//  - V1.0: (2018-08-04)定时器中断初始化，定时器中断服务函数
// 调试工具: 凌智STM32F429+Cyclone IV电子系统设计开发板、LZE_ST_LINK2
// 说    明: 
//    
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// 头文件包含
//-----------------------------------------------------------------
#include "tim_freq.h"
//-----------------------------------------------------------------

//用于采频率 最高32M左右
//PA0是输入信号


TIM_HandleTypeDef TIM2_Handler;         //定时器2句柄 
TIM_HandleTypeDef TIM3_Handler;         //定时器3句柄 

extern u16 TIM2_IT_CNT;	// TIM2 中断次数计数
extern u16 TIM3_IT_CNT;	// TIM3 中断次数计数

u16 TIM2_IT_CNT_REG;		// TIM2 中断次数计数寄存器
u16 TIM3_IT_CNT_REG;		// TIM3 中断次数计数寄存器

extern u16 TIM2_CNT;		// TIM2计数器中计数值
extern u16 TIM3_CNT;		// TIM3计数器中计数值

extern u8  Mstatus;			// 测量状态

u16 TIM2_IT_CNT = 0;            // TIM2 中断次数计数
u16 TIM3_IT_CNT = 0;            // TIM3 中断次数计数
u16 TIM2_CNT = 0;                                // TIM2计数器中计数值
u16 TIM3_CNT = 0;                                // TIM3计数器中计数值
u32 Standard_Count = 0;                    // 闸门时间内标准信号的计数值
u32 Measured_Count = 0;                    // 闸门时间内待测信号的计数值
u8  Mstatus = MEASU_END;        // 测量状态：结束状态
//float Frequency = 0;                  // 频率
#define STANDERD_TIM_CLK 90000000.0f // 标准定时器时钟频率 (45MHz*2 = 90MHz)


void MY_TIM_init()
{
	TIM2_Init(0xFFFF,0);                    // 初始化定时器2
  TIM3_Init(0xFFFF,0);                     // 初始化定时器3
  Mstatus = MEASU_START;          // 测量状态：开始状态
  // 移除输入捕获启动，因为TIM2现在使用外部时钟源  
  HAL_TIM_IC_Start_IT( &TIM2_Handler, TIM_CHANNEL_1 ); // 启动定时器2  捕捉输入信号上升沿
  HAL_TIM_Base_Start_IT ( &TIM3_Handler );

}


void Get_Frequence(float *Frequency)
{
	 if(Mstatus == MEASU_END)  // 等待测量结束
	 {
		HAL_TIM_Base_Start_IT(&TIM3_Handler);                  // 启动定时器3  用于超时检测
		// 获取标准计数值
		Standard_Count = ( TIM3_IT_CNT << 16) + TIM3_CNT;
		// 获取待测计数值
		Measured_Count = ( TIM2_IT_CNT << 16) + TIM2_CNT;
		
		if ( Standard_Count == 0 )
			*Frequency = 0;
		else 
			*Frequency = (float)Measured_Count * STANDERD_TIM_CLK / Standard_Count;
					Mstatus = MEASU_START;   // 测量状态：开始测量
		
		HAL_TIM_IC_Start_IT ( &TIM2_Handler, TIM_CHANNEL_1 );  // 启动下一轮测量
	}
}
//-----------------------------------------------------------------
// vvoid TIM3_Init(u16 arr,u16 psc)
//-----------------------------------------------------------------
//
// 函数功能: 定时器3中断初始化
// 入口参数: u16 arr：自动重装值
//					 u16 psc：时钟预分频数
// 返回参数: 无
// 注意事项: 定时器溢出时间计算方法:Tout=((arr+1)*(psc+1))/Ft us.
//					 Ft=定时器工作频率,单位:Mhz
//					 这里使用的是定时器3!(定时器3挂在APB1上，时钟为HCLK/2)
//
//-----------------------------------------------------------------
void TIM3_Init(u16 arr,u16 psc)
{  
	TIM3_Handler.Instance=TIM3;                          // 通用定时器3
	TIM3_Handler.Init.Prescaler=psc;                     // 分频系数
	TIM3_Handler.Init.CounterMode=TIM_COUNTERMODE_UP;    // 向上计数器
	TIM3_Handler.Init.Period=arr;                        // 自动装载值
	TIM3_Handler.Init.ClockDivision=TIM_CLOCKDIVISION_DIV1;// 时钟分频因子
	HAL_TIM_Base_Init(&TIM3_Handler);
	__HAL_TIM_CLEAR_IT(&TIM3_Handler,TIM_IT_UPDATE);		 // 防止一开启定时器就进入中断
}

//-----------------------------------------------------------------
// vvoid TIM2_Init(u16 arr,u16 psc)
//-----------------------------------------------------------------
//
// 函数功能: 定时器2中断初始化
// 入口参数: u16 arr：自动重装值
//					 u16 psc：时钟预分频数
// 返回参数: 无
// 注意事项: 定时器溢出时间计算方法:Tout=((arr+1)*(psc+1))/Ft us.
//					 Ft=定时器工作频率,单位:Mhz
//					 这里使用的是定时器3!(定时器3挂在APB1上，时钟为HCLK/2)
//
//-----------------------------------------------------------------
void TIM2_Init(u16 arr,u16 psc)
{  
	TIM_ClockConfigTypeDef sClockSourceConfig;
	TIM_IC_InitTypeDef sConfigIC;
	
	TIM2_Handler.Instance=TIM2;                          // 通用定时器2
	TIM2_Handler.Init.Prescaler=psc;                     // 分频系数
	TIM2_Handler.Init.CounterMode=TIM_COUNTERMODE_UP;    // 向上计数器
	TIM2_Handler.Init.Period=arr;                        // 自动装载值
	TIM2_Handler.Init.ClockDivision=TIM_CLOCKDIVISION_DIV1;// 时钟分频因子
	HAL_TIM_Base_Init(&TIM2_Handler);


	sClockSourceConfig.ClockSource=TIM_CLOCKSOURCE_ETRMODE2;				// TIM时钟源
	sClockSourceConfig.ClockFilter = 0x0000;												// 时钟滤波器
  sClockSourceConfig.ClockPolarity = TIM_CLOCKPOLARITY_NONINVERTED;	// 时钟极性
  sClockSourceConfig.ClockPrescaler = TIM_CLOCKPRESCALER_DIV1;		// 时钟预分频器
  HAL_TIM_ConfigClockSource(&TIM2_Handler, &sClockSourceConfig);
	HAL_TIM_IC_Init(&TIM2_Handler);
	
	sConfigIC.ICPolarity = TIM_INPUTCHANNELPOLARITY_RISING;	// 上升沿捕获
  sConfigIC.ICSelection = TIM_ICSELECTION_DIRECTTI;				// 映射到TI上
  sConfigIC.ICPrescaler = TIM_ICPSC_DIV1;									// 输入分频，不分频
  sConfigIC.ICFilter = 0;																	// 输入滤波器，不滤波
	HAL_TIM_IC_ConfigChannel(&TIM2_Handler, &sConfigIC, TIM_CHANNEL_1);
}

//-----------------------------------------------------------------
// void HAL_TIM_Base_MspInit(TIM_HandleTypeDef *htim)
//-----------------------------------------------------------------
//
// 函数功能: 定时器底册驱动，开启时钟，设置中断优先级
// 入口参数: TIM_HandleTypeDef *htim：定时器3句柄
// 返回参数: 无
// 注意事项: 此函数会被HAL_TIM_Base_Init()函数调用
//
//-----------------------------------------------------------------
void HAL_TIM_Base_MspInit(TIM_HandleTypeDef *htim)
{
	GPIO_InitTypeDef GPIO_Initure;
  if(htim->Instance==TIM2)
	{
		__HAL_RCC_TIM2_CLK_ENABLE();          // 使能TIM2时钟
		__HAL_RCC_GPIOA_CLK_ENABLE();					// 开启GPIOB时钟
		GPIO_Initure.Pin=GPIO_PIN_0;          // PA0
		GPIO_Initure.Mode=GPIO_MODE_AF_PP;    // 复用推挽输出
		GPIO_Initure.Pull = GPIO_NOPULL;			// 无上下拉
		GPIO_Initure.Speed=GPIO_SPEED_HIGH;   // 高速
		GPIO_Initure.Alternate=GPIO_AF1_TIM2;
		HAL_GPIO_Init(GPIOA,&GPIO_Initure);
		
		HAL_NVIC_SetPriority(TIM2_IRQn,0,0);  // 设置中断优先级，抢占优先级0，子优先级0
		HAL_NVIC_EnableIRQ(TIM2_IRQn); 				// 开启ITM2中断  
	}
	
	if(htim->Instance==TIM3)
	{
		__HAL_RCC_TIM3_CLK_ENABLE();          // 使能TIM3时钟
		HAL_NVIC_SetPriority(TIM3_IRQn,1,1);  // 设置中断优先级，抢占优先级1，子优先级1 
		HAL_NVIC_EnableIRQ(TIM3_IRQn);        // 开启ITM3中断   
	}
}

//-----------------------------------------------------------------
// void TIM2_IRQHandler(void)
//-----------------------------------------------------------------
//
// 函数功能: 定时器2中断服务函数
// 入口参数: 无
// 返回参数: 无
// 注意事项: 无
//
//-----------------------------------------------------------------
void TIM2_IRQHandler(void)
{
    HAL_TIM_IRQHandler(&TIM2_Handler);
}

//-----------------------------------------------------------------
// void TIM3_IRQHandler(void)
//-----------------------------------------------------------------
//
// 函数功能: 定时器3中断服务函数
// 入口参数: 无
// 返回参数: 无
// 注意事项: 无
//
//-----------------------------------------------------------------
void TIM3_IRQHandler(void)
{
    HAL_TIM_IRQHandler(&TIM3_Handler);
}

//-----------------------------------------------------------------
// void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
//-----------------------------------------------------------------
//
// 函数功能: 定时器中断服务函数
// 入口参数: TIM_HandleTypeDef *htim：定时器3句柄
// 返回参数: 无
// 注意事项: 无
//
//-----------------------------------------------------------------
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
	if(htim==(&TIM2_Handler))
	{
		TIM2_IT_CNT_REG ++;
	}
	if(htim==(&TIM3_Handler))
	{
		TIM3_IT_CNT_REG ++;
    if ( TIM3_IT_CNT_REG == TUEN_TIME_CNT )                         // 砸门时间到
    {
      HAL_TIM_IC_Start_IT (  &TIM2_Handler, TIM_CHANNEL_1 );        // 捕捉输入信号上升沿，用于结束测量
      __HAL_TIM_CLEAR_IT(htim, TIM_IT_CC1);                         // 清除中断标志
    }
		else if ( TIM3_IT_CNT_REG > OVER_TIME_CNT )                     // 超时   
    {
      Mstatus = MEASU_END;                                          // 测量状态为：结束
      
      HAL_TIM_IC_Stop_IT (  &TIM2_Handler, TIM_CHANNEL_1 );         // 关闭捕捉功能
      //__HAL_TIM_CLEAR_IT(htim, TIM_IT_CC1);                         // 清除中断标志
      
      HAL_TIM_Base_Stop_IT ( &TIM2_Handler );                       // 关闭待测计数器
      HAL_TIM_Base_Stop_IT ( &TIM3_Handler );                       // 关闭标准计数器
      
      __HAL_TIM_CLEAR_FLAG( &TIM2_Handler, TIM_IT_UPDATE);            // 清除中断标志
      __HAL_TIM_CLEAR_FLAG( &TIM3_Handler, TIM_IT_UPDATE);            // 清除中断标志
      
      TIM2_IT_CNT = 0;
      TIM3_IT_CNT = 0;
      
      TIM2_CNT = 0;
      TIM2_CNT = 0;

    }
	}
}

//-----------------------------------------------------------------
// void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
//-----------------------------------------------------------------
//
// 函数功能: 定时器2输入捕获中断
// 入口参数: TIM_HandleTypeDef *htim：定时器3句柄
// 返回参数: 无
// 全局变量: 无 
// 注意事项: 无
//-----------------------------------------------------------------
void HAL_TIM_IC_CaptureCallback(TIM_HandleTypeDef *htim)
{
	if(htim==(&TIM2_Handler))
	{
		if( Mstatus == MEASURING )                             // 结束测量
    {
      Mstatus = MEASU_END;                                 // 测量状态为：完成
      
			if(__HAL_TIM_GET_FLAG( &TIM2_Handler, TIM_IT_UPDATE))// 测量结束时判断TIM2是否刚好进入中断
				TIM2_IT_CNT_REG ++;
			
      HAL_TIM_IC_Stop_IT   ( htim, TIM_CHANNEL_1 );        // 关闭输入捕捉功能  
      __HAL_TIM_CLEAR_IT(htim, TIM_IT_CC1);                // 清除中断标志
      
      HAL_TIM_Base_Stop_IT ( &TIM2_Handler );              // 关闭待测计数器
      HAL_TIM_Base_Stop_IT ( &TIM3_Handler );              // 关闭标准计数器
      
      TIM2_IT_CNT = TIM2_IT_CNT_REG;
      TIM3_IT_CNT = TIM3_IT_CNT_REG;
      
      TIM3_CNT = __HAL_TIM_GET_COUNTER ( &TIM3_Handler );
      TIM2_CNT = __HAL_TIM_GET_COUNTER ( &TIM2_Handler );
      
    }
    else if ( Mstatus == MEASU_START )                     // 启动测量
    {
      Mstatus = MEASURING;                                 // 测量状态：测量中
      HAL_TIM_IC_Stop_IT   ( htim, TIM_CHANNEL_1 );        // 关闭输入捕捉功能     
      //__HAL_TIM_CLEAR_IT(htim, TIM_IT_CC1);                // 清除中断标志
      
      // 清除计数器
      __HAL_TIM_SET_COUNTER( &TIM3_Handler, 0 );
      __HAL_TIM_SET_COUNTER( &TIM2_Handler, 0 );

      TIM2_IT_CNT_REG = 0;
      TIM3_IT_CNT_REG = 0;

      __HAL_TIM_CLEAR_FLAG( &TIM2_Handler, TIM_IT_UPDATE);   // 清除中断标志
      __HAL_TIM_CLEAR_FLAG( &TIM3_Handler, TIM_IT_UPDATE);   // 清除中断标志
      
      HAL_TIM_Base_Start_IT ( &TIM2_Handler );             // 启动定时器2  待测信号计数
      HAL_TIM_Base_Start_IT ( &TIM3_Handler );             // 启动定时器3  标准信号计数
    }
	}
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
